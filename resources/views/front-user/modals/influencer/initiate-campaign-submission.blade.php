{{--
When the influencer initiate the campaign submission from /active-campaigns page, this modal shows up after 
the get ajax request to '/initiate-influencer-campaign-submission/{id?}'

**The controller method is called to build the view
[InfluencerSubmissionController::class, 'initiateInfluencerCampaignSubmission']
--}}

<div class="modal fade confirm-content influncer" id="influencerInitiatedCampaignSubmission{{ $influencerDataItem->id }}"
    data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
    aria-labelledby="requestSubmit{{ $influencerDataItem->id }}Label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <img src="{{ asset('/assets/front-end/images/icons/close-one.svg') }}" alt="">
                </button>
                <div class="wizardHeading">Confirm the content for the campaign</div>
                <div>
                    <div class="campHistory" border="0" cellpedding="0" cellspacing="0">
                        {{-- Will be filled up after ajax call --}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--end  modal for request time -->