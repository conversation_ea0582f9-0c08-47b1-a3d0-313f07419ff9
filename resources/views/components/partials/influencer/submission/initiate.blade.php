<style>
    .submission-timing {
        display: inline-block;
        border: 1px solid #868686;
        border-radius: 10px;
        padding: 5px 8px;
        background-color: #f9f9f9;
        box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.2);
        color: #868686;
        font-weight: bold;
    }
</style>
<div>
    <div class="d-flex hhsu">
        <div class="soclDetail">
            <div class="media-div">
                <img src="{{ asset('assets/front-end/images/icons/campaigns-' . $influencerRequestDetail->media . '.svg') }}" class="standard-icon">
            </div>
        </div>
        <div class="admethod">
            <span> {{ $showTitle }}</span>
        </div>
    </div>

    @if ($validSocialPostCount > 0)
        @foreach ($renderPartials as $renderPartialName => $socialPost)
            @include($renderPartialName, ['socialPost' => $socialPost, 'influencerRequestDetail' => $influencerRequestDetail, 'validSocialPostCount' => $validSocialPostCount])
        @endforeach
    @else
        @include('components.partials.influencer.submission.socialpost.no-valid-social-post-for-submission', ['influencerRequestDetail' => $influencerRequestDetail, 'validSocialPostCount' => $validSocialPostCount])
    @endif

    <div class="text-center button-text">
        <p>Your content does not show up?</p>
        @if ($influencerRequestDetail->is_paused == 1)
            <button type="button" class="table-btn red-btn ds" style="width: 165px !important;" disabled>Paused</button>
        @else
            <a href="#" class="table-btn red-btn ds" data-toggle="pause_campaign" data-id="{{ $influencerRequestDetail->id }}" style="width: 165px !important;">Pause campaign</a>
        @endif
    </div>
</div>

@if ($validSocialPostCount > 0)
    <script type="text/javascript">
        var socialPosts = @json($socialPosts);
        if (socialPosts.length > 0) {
            socialPosts.forEach(timer);

            function timer(socialPost, index) {
                console.log('******************* initiate.blade.php');
                console.log('========== socialPost: ');
                console.log(socialPost);
                console.l
                // Sample data
                // {
                //     "id": 1420,
                //     "influencer_request_accept_id": "story",
                //     "user_id": "114",
                //     "post_id": "18054786557580877",
                //     "media": "instagram",
                //     "text": "C282",
                //     "link": "social_pics/18054786557580877_instagram.jpg",
                //     "type": "photo",
                //     "thumbnail": "https://instagram.com/stories/motktest/3677269399785693875",
                //     "created_at": "2025-07-15T14:00:28.000000Z",
                //     "updated_at": "2025-07-16T06:31:34.000000Z",
                //     "published_at": "2025-07-15T13:04:50.000000Z",
                //     "like": 0,
                //     "comment": 0,
                //     "view": 0,
                //     "share": 0,
                //     "friend": 0,
                //     "favorite": 0,
                //     "insights": []
                // }
                // Convert "YYYY-MM-DD HH:mm:ss" to UTC Date object
                // socialPost
                var storyDateParts = socialPost.published_at.split(' ');
                var dateParts = storyDateParts[0].split('-');
                var timeParts = storyDateParts[1].split(':');
                var storyDate = new Date(Date.UTC(
                    parseInt(dateParts[0]),           // year
                    parseInt(dateParts[1]) - 1,       // month (0-based)
                    parseInt(dateParts[2]),           // day
                    parseInt(timeParts[0]),           // hour
                    parseInt(timeParts[1]),           // minute
                    parseInt(timeParts[2])            // second
                ));

                storyDate.setDate(storyDate.getDate() + 1);
                storyDate = storyDate.getTime();
                var now = new Date();
                // Update the count down every 1 second
                var x = setInterval(function() {
                    // Get today's date and time
                    var now = new Date().getTime();
                    // Find the distance between now and the count down date
                    var distance = storyDate - now;
                    // Time calculations for days, hours, minutes and seconds
                    var days = Math.floor(distance / (1000 * 60 * 60 * 24));
                    var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                    var seconds = Math.floor((distance % (1000 * 60)) / 1000);

                    if (days < 10 && days != 0) {
                        days = '0' + days;
                    }
                    if (hours < 10) {
                        hours = '0' + hours;
                    }
                    if (minutes < 10) {
                        minutes = '0' + minutes;
                    }
                    if (seconds < 10) {
                        seconds = '0' + seconds;
                    }
                    // Output the result in an element with id="demo"
                    $(".story_timer" + socialPost.id).text(days + " d " + hours + " h " + minutes + " min");
                    
                    // If the count down is over, write some text
                    if (distance < 0) {
                        clearInterval(x);
                        $("#story_timer" + socialPost.id).text("EXPIRED");
                    }
                }, 1000);
            }
        }
    </script>
@endif

<script type="text/javascript">
    function confirmInfluencerSubmission(btn) {
        const $btn = $(btn);
        // Helper function to safely parse JSON or return original value
        function safeParseJSON(value) {
            try {
                return JSON.parse(value); // Try parsing as JSON
            } catch (e) {
                return value; // If parsing fails, return the original value
            }
        }

        // See the sample data below
        const influencer_request_id = safeParseJSON($btn.data('influencer_request_id'));
        const post_id = safeParseJSON($btn.data('post_id'));
        const advertising = safeParseJSON($btn.data('advertising'));
        const media = safeParseJSON($btn.data('media'));
        const text = safeParseJSON($btn.data('text'));
        const media_url = safeParseJSON($btn.data('media_url'));
        const type = safeParseJSON($btn.data('type'));
        const post_content_type = safeParseJSON($btn.data('post_content_type'));
        const published_at = safeParseJSON($btn.data('published_at'));
        const post_type = safeParseJSON($btn.data('post_type'));
        const thumbnail = safeParseJSON($btn.data('thumbnail'));

        const like = safeParseJSON($btn.data('like')) || 0;
        const comment = safeParseJSON($btn.data('comment')) || 0;
        const view = safeParseJSON($btn.data('view')) || 0;
        const share = safeParseJSON($btn.data('share')) || 0;
        const friend = safeParseJSON($btn.data('friend')) || 0;
        const favorite = safeParseJSON($btn.data('favorite')) || 0;

        // Sample data
        // influencer_request_id: 531
        // post_id: 1396
        // advertising: Reel
        // media: instagram
        // text: C269#
        // media_url: social_pics/18487344784064753_instagram.mp4
        // type: video
        // post_content_type: content
        // published_at: 2025-06-26 12:53:36
        // post_type: Reaction video
        // thumbnail: https://www.instagram.com/reel/DLXVww1o1DN/
        // like: 4
        // comment: 1
        // view: 19
        // share: 6
        // friend: 0
        // favorite: 0

        console.log('Initiated **************');

        console.log('influencer_request_id:', influencer_request_id);
        console.log('post_id:', post_id);
        console.log('advertising:', advertising); // this is the select_type in tasks table
        console.log('media:', media);
        console.log('text:', text);
        console.log('media_url:', media_url);
        console.log('type:', type); // then what is this? (I guess this might be the social media post content type, can be video or photo)
        console.log('post_content_type:', post_content_type); // this is the post_type_content aka ptc in the influencer_request_details table
        console.log('published_at:', published_at);
        console.log('post_type:', post_type); // this is the type in the tasks table
        console.log('thumbnail:', thumbnail);
        console.log('like:', like);
        console.log('comment:', comment);
        console.log('view:', view);
        console.log('share:', share);
        console.log('friend:', friend);
        console.log('favorite:', favorite);

        const socialMediaPostUrl = thumbnail;
        const socialMediaIconUrl = '{{ asset("/assets/front-end/images/icons/campaigns-") }}' + media + '.svg';
        const socialMediaPostLocalFilePath = media_url;

        var socialMediaPostLocalUrl = '';
        if (socialMediaPostLocalFilePath) {
            socialMediaPostLocalUrl = '{{ url("storage") . '/' }}' + socialMediaPostLocalFilePath;
        }

        console.log('After fixing the variables >>>>>>>>>>>');
        console.log('socialMediaPostLocalFilePath: ' + socialMediaPostLocalFilePath);
        console.log('socialMediaPostUrl: ' + socialMediaPostUrl);
        console.log('socialMediaPostLocalUrl: ' + socialMediaPostLocalUrl);
        console.log('socialMediaIconUrl: ' + socialMediaIconUrl);

        // Now we populate data for the modal here
        // resources/views/front-user/modals/influencer/confirm-campaign-submission.blade.php
        var targetInfluencerConfirmCampaignSubmissionId = "#influencerConfirmCampaignSubmission" + influencer_request_id;

        $(targetInfluencerConfirmCampaignSubmissionId + ' #content_text').html(text);
        $(targetInfluencerConfirmCampaignSubmissionId + ' #content_influencer_request_id').val(influencer_request_id);
        $(targetInfluencerConfirmCampaignSubmissionId + ' #content_post_id').val(post_id);
        $(targetInfluencerConfirmCampaignSubmissionId + ' #content_advertising').val(advertising);
        $(targetInfluencerConfirmCampaignSubmissionId + ' #content_media').val(media);
        $(targetInfluencerConfirmCampaignSubmissionId + ' #content_media_url').val(socialMediaPostLocalFilePath);
        $(targetInfluencerConfirmCampaignSubmissionId + ' #content_url').attr('href', socialMediaPostLocalFilePath);
        $(targetInfluencerConfirmCampaignSubmissionId + ' #content_url_img').hide();
        $(targetInfluencerConfirmCampaignSubmissionId + ' #content_url_video_tag').hide();
        $(targetInfluencerConfirmCampaignSubmissionId + ' #viewLink').hide();

        $(targetInfluencerConfirmCampaignSubmissionId + ' #social-media-icon').attr('src', socialMediaIconUrl);

        $.ajax({
            url: "{{ URL::to('/get-influencer-tasks') }}",
            method: 'GET',
            data: {
                'media': media,
                'type': post_type,
                'advertising': advertising,
                'select_type': advertising,
                'post_type_content': post_content_type,
                'ptc': post_content_type,
                'post_content_type': post_content_type,
                'influencer_request_id': influencer_request_id
            },
        }).done(function(data) {
            $(targetInfluencerConfirmCampaignSubmissionId + ' .tasklists').html(data);
            var input_length = $(targetInfluencerConfirmCampaignSubmissionId + " .velidation-additional-tasks .form-check").length

            $(targetInfluencerConfirmCampaignSubmissionId + " .velidation-additional-tasks .form-check input").attr("data-parsley-mincheck", input_length)
        }).fail(function() {});

        if (socialMediaPostLocalUrl) {
            if (type == 'video') {
                $(targetInfluencerConfirmCampaignSubmissionId + ' #content_url_video_tag').show();
                $(targetInfluencerConfirmCampaignSubmissionId + ' #content_url_video').attr('src', socialMediaPostLocalUrl);
                $(targetInfluencerConfirmCampaignSubmissionId + ' #content_url_video_tag')[0].load();
                $(targetInfluencerConfirmCampaignSubmissionId + ' #content_url_img').hide();
            }

            if (type == 'photo') {
                $(targetInfluencerConfirmCampaignSubmissionId + ' #content_url_img').show();
                $(targetInfluencerConfirmCampaignSubmissionId + ' #content_url_img').attr('src', socialMediaPostLocalUrl);
                $(targetInfluencerConfirmCampaignSubmissionId + ' #content_url_video_tag').hide();
            }

            $(targetInfluencerConfirmCampaignSubmissionId + ' #viewLink').show();
            $(targetInfluencerConfirmCampaignSubmissionId + ' #viewLink').attr('href', socialMediaPostUrl);
            $(targetInfluencerConfirmCampaignSubmissionId + ' #viewLink').html(socialMediaPostUrl);
        }

        $(targetInfluencerConfirmCampaignSubmissionId + ' #content_published_at').val(published_at);

        // Not being used now
        $(targetInfluencerConfirmCampaignSubmissionId + ' #favoritediv').hide();

        // The are only being used
        $(targetInfluencerConfirmCampaignSubmissionId + ' #frienddiv').hide();
        $(targetInfluencerConfirmCampaignSubmissionId + ' #likediv').hide();
        $(targetInfluencerConfirmCampaignSubmissionId + ' #commentdiv').hide();
        $(targetInfluencerConfirmCampaignSubmissionId + ' #viewdiv').hide();
        $(targetInfluencerConfirmCampaignSubmissionId + ' #sharediv').hide();

        if (advertising == 'Story') {
            $(targetInfluencerConfirmCampaignSubmissionId + ' #friend').html(comment);
            $(targetInfluencerConfirmCampaignSubmissionId + ' #frienddiv').show();

            $(targetInfluencerConfirmCampaignSubmissionId + ' #view').html(view);
            $(targetInfluencerConfirmCampaignSubmissionId + ' #viewdiv').show();

            // This is actually total_interactions here
            $(targetInfluencerConfirmCampaignSubmissionId + ' #like').html(like);
            $(targetInfluencerConfirmCampaignSubmissionId + ' #likediv').show();
            $(targetInfluencerConfirmCampaignSubmissionId + ' #likediv').attr('title', 'Total number of likes, saves, comments, and shares.');

            $(targetInfluencerConfirmCampaignSubmissionId + ' #share').html(share);
            $(targetInfluencerConfirmCampaignSubmissionId + ' #sharediv').show();
        } else {
            // This is actually the reach here
            $(targetInfluencerConfirmCampaignSubmissionId + ' #share').html(share);
            $(targetInfluencerConfirmCampaignSubmissionId + ' #sharediv').show();

            $(targetInfluencerConfirmCampaignSubmissionId + ' #view').html(view);
            $(targetInfluencerConfirmCampaignSubmissionId + ' #viewdiv').show();

            $(targetInfluencerConfirmCampaignSubmissionId + ' #like').html(like);
            $(targetInfluencerConfirmCampaignSubmissionId + ' #likediv').show();
            $(targetInfluencerConfirmCampaignSubmissionId + ' #likediv').attr('title', 'Total number of likes.');

            $(targetInfluencerConfirmCampaignSubmissionId + ' #commentSet').html(comment);
            $(targetInfluencerConfirmCampaignSubmissionId + ' #commentdiv').show();
        }

        if (post_type == 'Polls') {
            $(targetInfluencerConfirmCampaignSubmissionId + ' #content_display').html('Survey title');
        }
    }
</script>
